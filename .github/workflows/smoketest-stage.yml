name: sfpApps Smoke Test

on:
  push:
    branches:
      - develop
  workflow_dispatch:
  pull_request_review:
    types: [ submitted ]


concurrency:
  group: deploy-kr-stage
  cancel-in-progress: true

env:
  ARTIFACTORY_EDGE_USERNAME_REF: ${{secrets.ARTIFACTORY_EDGE_USERNAME}}
  ARTIFACTORY_EDGE_TOKEN_REF: ${{secrets.ARTIFACTORY_EDGE_TOKEN}}

jobs:
  build:
    runs-on: aks
    container: maven:3.9.8-eclipse-temurin-11
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4


      - name: Run Tests
        run: |
          mvn clean test --settings settings.xml -Dtest=SmokeTestRunner -Dclient_id=${{ secrets.CLIENT_ID }} -Dclient_secret=${{ secrets.CLIENT_SECRET }} -Dkafka_pwd=${{ secrets.KAFKA_PWD }}

      - name: QMetry
        if: always()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName="Event manager karate Regression Tests"

      - name: Upload artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Kara<PERSON> Cucumber Reports
          path: ./target/cucumber-html-reports

      - name: Action JUnit Report | Upload Test Summary
        uses: mikepenz/action-junit-report@v3 # Generate "Tests env summary"
        if: success() || failure() # always run even if the previous step fails
        with:
          report_paths: "**/target/karate-reports/*.xml"
          detailed_summary: true

      - name: Upload results to Qmetry
        if: success() || failure()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName=Karate_Assortment_Item -Denviroment="${{ github.event.inputs.env || env.DEFAULT_ENV }}"

      - name: Upload artifacts
        if: success() || failure()
        uses: actions/upload-artifact@v4
        with:
          name: Karate Cucumber Reports
          path: ./karate-api-testing/target/cucumber-html-reports