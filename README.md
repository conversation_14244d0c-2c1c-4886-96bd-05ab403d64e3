# sfpApps-karate-framework
creating api and UI automation test cases for sfp apps
This repo is dedicated to the API test automation of sfpApps  team. The automation of test cases have been processed using the Karate framework. We have incorporated the DDT (Data-Driven Testing approach) for the automation. The csv files have been placed within the src/test/resources/data folder, and data are dynamically reading with the help of keywords leveraged by the Karate such as Scenario Outline and Examples

Prime Validations Conducted in the Tests that we Automated

Data Validation
Status Code Validation
Schema Validation
Header Validation
Dependencies

pom.xml file contains all dependency libraries required for the project

Annotations

@Test - To run test cases
@ignore - To ignore or avoid the test cases
@smoke - The Test Cases is considered as smoke test
@regression - The Test Cases is considered as regression test
Generic Command to run regression test cases

mvn clean test --settings settings.xml -Dtest=com.krogerqa.sfpAppsKarate.RegressionTestRunner

Generic Command to run smoke test cases

mvn clean test --settings settings.xml -Dtest=SmokeTestsRunner
mvn clean test --settings settings.xml -Dtest=RegressionTestRunner
mvn clean test --settings settings.xml -Dtest=smokeTestRunner
mvn clean test --settings settings.xml -Dclient_id={{client_id}} -Dclient_secret={{client_secret}} -Dtest=RegressionTestRunner
mvn -Dkarate.options="--tags @<tag>" -Dtest=SmokeTestRunner -Dclient_id={{client_id}} -Dclient_secret={{client_secret}} test

