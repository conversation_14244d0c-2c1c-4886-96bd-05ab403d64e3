<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                        http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
        <server>
            <username>${env.ARTIFACTORY_EDGE_USERNAME_REF}</username>
            <password>${env.ARTIFACTORY_EDGE_TOKEN_REF}</password>
            <id>artcentral</id>
        </server>
        <server>
            <username>${env.ARTIFACTORY_EDGE_USERNAME_REF}</username>
            <password>${env.ARTIFACTORY_EDGE_TOKEN_REF}</password>
            <id>artsnapshots</id>
        </server>
        <server>
            <username>${env.ARTIFACTORY_EDGE_USERNAME_REF}</username>
            <password>${env.ARTIFACTORY_EDGE_TOKEN_REF}</password>
            <id>artremote</id>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <mirrorOf>*,!artcentral,!artsnapshots,!artremote</mirrorOf>
            <name>remote-repos</name>
            <url>https://artifactory-edge.kroger.com/artifactory/remote-repos</url>
            <id>artremote</id>
        </mirror>
        <mirror>
            <id>maven-default-http-blocker</id>
            <mirrorOf>external-repos</mirrorOf>
            <name>external-repos to override blocking of http</name>
            <url>http://0.0.0.0/</url>
        </mirror>
    </mirrors>
    <profiles>
        <profile>
            <repositories>
                <repository>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <id>artcentral</id>
                    <name>libs-release</name>
                    <url>https://artifactory-edge.kroger.com/artifactory/libs-release</url>
                </repository>
                <repository>
                    <snapshots />
                    <id>artsnapshots</id>
                    <name>libs-snapshot</name>
                    <url>https://artifactory-edge.kroger.com/artifactory/libs-snapshot</url>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <id>artcentral</id>
                    <name>plugin-release</name>
                    <url>https://artifactory-edge.kroger.com/artifactory/plugins-release</url>
                </pluginRepository>
                <pluginRepository>
                    <snapshots />
                    <id>artsnapshots</id>
                    <name>plugin-snapshot</name>
                    <url>https://artifactory-edge.kroger.com/artifactory/plugins-snapshot</url>
                </pluginRepository>
            </pluginRepositories>
            <id>artifactory</id>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>artifactory</activeProfile>
    </activeProfiles>
</settings>

