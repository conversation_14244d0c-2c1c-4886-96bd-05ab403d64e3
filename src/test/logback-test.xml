<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>target/karate.log</file>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.intuit.karate" level="INFO"/>
    <!--    <logger name="demo" level="DEBUG"/>-->
    <!--    <logger name="mock" level="DEBUG"/>-->

    <root level="warn">
        <appender-ref ref="STDOUT" />
        <!--        <appender-ref ref="FILE" />-->
    </root>

</configuration>