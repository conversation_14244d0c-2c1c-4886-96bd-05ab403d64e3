# PLAIN
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
ssl.truststore.location=src/test/resources/kafka.client.truststore.jks
ssl.truststore.password=changeit
## Project configs
bootstrap.servers= u060onyxd201.kroger.com:9092,u060onyxd203.kroger.com:9092,u060onyxd205.kroger.com:9092,u060onyxd207.kroger.com:9092,u060onyxd209.kroger.com:9092
schema.registry.url = http://u060onyxm201.kroger.com:8081,http://u060onyxm202.kroger.com:8081
## Consumer
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer
## Producer
key.serializer=org.apache.kafka.common.serialization.StringSerializer
value.serializer=org.apache.kafka.common.serialization.StringSerializer