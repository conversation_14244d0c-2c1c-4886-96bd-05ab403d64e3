Feature: Create ,Get and Delete Custom Store Group

  Background:
    * configure ssl = true
    * configure url = em_base_url
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/EventManager/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def utils = Java.type(testUtil_classpath)
    * def name = utils.generateRandomString(4)

  @smoke @CSG
  Scenario: create custom store group
    Given path '/assortment/storeGroup/customStoreGroup'
    When request {"category": "EM","name": '#(name)',"revBy": "DOU4443","revByName": "SHARMA, VARUN","division": "014","stores": ["00335","00336","00344","00351"]}
    And method POST
    Then status 201
    * print response.data.groupId
#    Get CSG
    * def groupId = response.data.groupId
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    Then status 200
#    Schema validation
    * def schema12 = read('classpath:schema/EventManagerServiceSchema/CustomStoreGroup.json')
    * match response == schema12
#    Delete CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method DELETE
    * print response
    Then status 204
#    Get the deleted CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    * print response
    Then status 404