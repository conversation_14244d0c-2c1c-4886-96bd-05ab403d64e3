Feature: Create ,Get and Delete Custom Store Group Remodel

  Background:
    * configure ssl = true
    * configure url = em_base_url
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/EventManager/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def utils = Java.type(testUtil_classpath)
    * def name = utils.generateRandomString(4)

  @remodelKompass
  Scenario: create custom store group
    Given path '/assortment/storeGroup/customStoreGroup'
    And request {"category": "KE","name": '#(name)',"revBy": "DOU4443","revByName": "SHARMA, VARUN","division": "014","stores": ["00335","00336","00344","00351"]}
    When method POST
    Then status 201
#    Get Kompass CSG
    Given def groupId = response.data.groupId
    And header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    * print response
    Then status 200
#    Schema validation
    * def schema12 = read('classpath:schema/EventManagerServiceSchema/RemodelStores.json')
    * match response == schema12
#    Delete CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method DELETE
    * print response
    Then status 204
#    Get the deleted CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    * print response
    Then status 404