
Feature: Create Compliance Event

  Background:
    * configure ssl = true
    * configure url = em_kafka_base_url_root
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/EventManager/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def recordId = TestUtil.generateRandomString(10)
    * def consumer = TestUtil.setupKafkaConsumer(read(kafkaPropertiesPath), kaUser, kafkaTopic, kafkaTempFilePath)

  @smoke @ComplianceEvent @kafka
  Scenario Outline: create compliance event
    * print 'Using URL:', em_kafka_base_url_root + complianceEventPath
    * path complianceEventPath
    * def eventId = TestUtil.generate6DigitNumericString()
    * def currentTimeStr = defaultTimeStr
    * def body = read('classpath:data/Input/ComplianceEvent/Compliance.json')[<index>]
    * print 'Request body from file:', body
    * request body
    * method POST
    * print 'Response status:', responseStatus
    * print 'Response type:', karate.typeOf(response)
    Then status 200
    * print 'Waiting for message to be published to Kafka (' + kafkaWaitTime + ' milliseconds)...'
    * sleep(kafkaWaitTime)

    * def testMessages = consumer.getMessagesContainingValue(eventId)
    * def rawMessage = testMessages.toString()
    * print 'Raw message from Kafka:', rawMessage

    * def validationResult = TestUtil.validateKafkaMessage(rawMessage, eventId)
    * print 'Message contains eventId:', validationResult
    * assert validationResult == true

    # Close the consumer
    * consumer.close()
    * print 'TEST COMPLETED SUCCESSFULLY'

    Examples:
      | index |
      | 0     |
