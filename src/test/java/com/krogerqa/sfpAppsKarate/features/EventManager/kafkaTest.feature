Feature: SFPApps Kafka test

  Background:
    * configure ssl = true
    * def Random = Java.type('java.util.Random')
    * def RandomStringUtils = Java.type('org.apache.commons.lang3.RandomStringUtils')
    # Set up Kafka with properties from config
    * def consumer = TestUtil.setupKafkaConsumer(read(kafkaPropertiesPath), kaUser, kafkaTopic, kafkaTempFilePath)

  @kafkaTest
  Scenario: Random Text
    * def randomV = function(){ return java.util.UUID.randomUUID() + '' }
    * print randomV()
    * print new Random().nextInt(10000)
    * string generatedString = RandomStringUtils.random(10, true, true);
    * print generatedString

  @kafkaTest
  Scenario: Test
    * def producer = TestUtil.createKafkaProducer(kafkaTopic, filePath)
    * string generatedString = RandomStringUtils.random(10, true, true)
    * string notes = "Karate-Kafka-1123"+generatedString
    * print 'Notes: '+notes
    * producer.sendMessage("12638", '{"message": "Hello this is a test message from <PERSON><PERSON>", "notes": "' + notes + '"}')
    * producer.close()
    * print 'Waiting for message to be published to Kafka (' + kafkaWaitTime + ' milliseconds)...'
    * sleep(kafkaWaitTime)
    * def messages = consumer.getMessagesContainingValue(notes)
    * consumer.close()
    * print 'Message: '+messages
    * def validationResult = TestUtil.validateKafkaMessage(messages.toString(), notes)
    * print 'Validation result:', validationResult
    * assert validationResult == true
    * json out = messages
    * print 'json out: '+out