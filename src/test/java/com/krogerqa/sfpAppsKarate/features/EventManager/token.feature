Feature: generate token for CSG

  Background: ssl
    * configure ssl = true

  @authToken
  Scenario: Get oAuth write token for Item Domain Read API
    Given url "https://api-ce.kroger.com/v1/connect/oauth2/token/"
    And form field grant_type = 'client_credentials'
    And form field scope = 'urn:com:kroger:sfdomn:storegroups:read urn:com:kroger:sfdomn:storegroups:write'
    And form field client_id = karate.properties['client_id']
    And form field client_secret = karate.properties['client_secret']
    When method post
#    Then print response
    * status 200
    * def bearerToken = response.token_type+" "+response.access_token


