Feature: ILP Details For Comparison Report

  Background:
    * configure ssl = true
    * configure url = navigator_base_url
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/EventManager/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def utils = Java.type(testUtil_classpath)

  @smoke @IlpDetailsReport
  Scenario Outline: Get ILP Details For Comparison Report
    Given path ilpDetailsComparisonReportPath
    * def requestBody = read('classpath:data/Input/Navigator/IlpDetailsComparisonReport.json')[<index>]
    * print 'Request body:', requestBody
    When request requestBody
    And method POST
    Then status 200
    * print 'Response:', response
    
    # Validate response structure
    * match response.message == "No Records Found "
    * match response.data == null
    * match response.status == true
    * match response.totalRecords == null
    
    * print 'TEST COMPLETED SUCCESSFULLY'

    Examples:
      | index |
      | 0     |
