package com.krogerqa.sfpAppsKarate;

import com.intuit.karate.Results;
import com.intuit.karate.Runner;
import com.intuit.karate.core.FeatureResult;
import com.krogerqa.karatecentral.reportportal.ReportPortalPublisher;
import com.krogerqa.karatecentral.utilities.BaseTestRunner;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


public class SmokeTestRunner extends BaseTestRunner {

  @Test
  public void smokeTestRun() {
    ReportPortalPublisher reporter = new ReportPortalPublisher();
    reporter.startLaunch();
    Results results = Runner.path("src/test/java/com/krogerqa/sfpAppsKarate/features")
        .outputJunitXml(true)
        .outputCucumberJson(true)
        .outputHtmlReport(true)
        .tags("@smoke")
        .parallel(1);
    Object[] featureResults = results.getFeatureResults().toArray();
    for (Object featureResult : featureResults)
      {
        reporter.startFeature((FeatureResult) featureResult);
        reporter.finishFeature((FeatureResult) featureResult);
      }
    reporter.finishLaunch();
    Assertions.assertTrue(results.getFailCount() == 0, results.getErrorMessages());
  }

}
