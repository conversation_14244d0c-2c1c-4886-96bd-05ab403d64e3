function karateConfig() {
    let env = karate.env; // get system property 'karate.env'
    karate.log('karate.env system property was:', env);

    if (!env) {
        env = 'stage';
    }

    // Get Kafka password from system properties, environment variable, or fallback to hardcoded value
   

    let config = {
        env: env,
        // Kafka configuration
        kaUser: 's5940despn',
        kafkaTopic: 'sfp-execution-event',
        kafkaPropertiesPath: 'classpath:plain.properties',
        kafkaTempFilePath: '/tmp/plain_tmp_kafka.properties',
        kafkaWaitTime: 20000, // 20 seconds default wait time for Kafka messages

        // API endpoints
        db_service_url: 'https://apd-db-service-test.kpsazc.dgtl.kroger.com',
        em_base_url: 'https://assortment-item.sfdomn-stage.kpsazc.dgtl.kroger.com',
        em_base_em_url: 'https://sfp-apps-em-stage.itmloc.rch-cdc-cxnonprod.kroger.com',
        em_kafka_base_url_root: 'https://sfpapps-event-manager-kafka-stage.itmloc.rch-cdc-cxnonprod.kroger.com',

        // API paths
        refreshEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/refresh-event',
        areaEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/area-event',
        complianceEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/compliancy-event',

        // Other configuration
        apiKey: 'Basic c3ZjODM3OGFyZWFwaXM6aGowUnFHOHZvM0xIRDFBS01hdkNoSQ==',
        testUtil_classpath: 'com.krogerqa.sfpAppsKarate.globalUtils.TestUtil',

        // Common test data
        defaultTimeStr: "Thu May 14 16:50:00 UTC 2025"
    };

    // Instantiate utils globally
    config.utils = Java.type(config.testUtil_classpath);
    config.TestUtil = Java.type('com.krogerqa.sfpAppsKarate.globalUtils.TestUtil');

    // Helper functions
    config.sleep = function(millis) {
        java.lang.Thread.sleep(millis);
    };

    // Set timeouts
    karate.configure('connectTimeout', 200000);
    karate.configure('readTimeout', 200000);

    return config;
}
